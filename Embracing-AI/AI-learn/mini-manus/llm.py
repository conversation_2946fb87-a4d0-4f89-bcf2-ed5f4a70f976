"""
LLM客户端模块
=============

提供与大语言模型交互的客户端，支持多种API提供商。
类比pytest的测试运行器，负责执行核心逻辑。

设计理念：
1. 统一接口，支持多种LLM提供商
2. 异步支持，提高性能
3. 错误处理和重试机制
4. Mock模式，便于测试和演示
"""

import asyncio
import json
import random
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod


class BaseLLMClient(ABC):
    """
    LLM客户端基类
    
    类比pytest的测试运行器基类，定义统一接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = config.get('model', 'unknown')
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', '')
        self.max_tokens = config.get('max_tokens', 1000)
        self.temperature = config.get('temperature', 0.7)
    
    @abstractmethod
    async def generate(self, system_prompt: str, user_prompt: str) -> str:
        """
        生成文本响应
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            生成的文本
        """
        pass


class MockLLMClient(BaseLLMClient):
    """
    Mock LLM客户端
    
    用于演示和测试，不需要真实的API调用。
    类比pytest的mock功能，提供可预测的响应。
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 预定义的响应模板 (类比pytest的测试数据)
        self.response_templates = {
            'calculation': {
                'summary': '数学计算任务',
                'analysis': '用户需要进行数学计算',
                'steps': [
                    {
                        'description': '执行数学计算',
                        'tool': 'calculator',
                        'args': {'expression': '{expression}'}
                    }
                ]
            },
            'file_operation': {
                'summary': '文件操作任务',
                'analysis': '用户需要进行文件操作',
                'steps': [
                    {
                        'description': '执行文件操作',
                        'tool': 'file_editor',
                        'args': {'action': 'info', 'path': 'example.txt'}
                    }
                ]
            },
            'python_code': {
                'summary': 'Python代码执行任务',
                'analysis': '用户需要执行Python代码',
                'steps': [
                    {
                        'description': '执行Python代码',
                        'tool': 'python_executor',
                        'args': {'code': '{code}'}
                    }
                ]
            },
            'default': {
                'summary': '通用任务',
                'analysis': '这是一个通用任务请求',
                'steps': [
                    {
                        'description': '执行简单计算演示',
                        'tool': 'calculator',
                        'args': {'expression': '2+2'}
                    }
                ]
            }
        }
    
    async def generate(self, system_prompt: str, user_prompt: str) -> str:
        """
        生成Mock响应
        
        根据用户输入的关键词，返回相应的JSON计划
        """
        
        # 模拟网络延迟 (类比真实API调用)
        await asyncio.sleep(random.uniform(0.5, 1.5))
        
        # 分析用户输入，选择合适的响应模板
        user_input_lower = user_prompt.lower()
        
        # 提取用户的实际请求 (简单的正则匹配)
        import re
        request_match = re.search(r'用户请求[：:]\s*(.+)', user_prompt)
        actual_request = request_match.group(1) if request_match else user_prompt
        
        # 根据关键词选择模板
        if any(keyword in user_input_lower for keyword in ['计算', '算', '+', '-', '*', '/', '数学']):
            template = self.response_templates['calculation'].copy()
            # 尝试提取数学表达式
            math_expr = self._extract_math_expression(actual_request)
            if math_expr:
                template['steps'][0]['args']['expression'] = math_expr
            else:
                template['steps'][0]['args']['expression'] = actual_request
                
        elif any(keyword in user_input_lower for keyword in ['文件', '读取', '写入', '保存', 'file']):
            template = self.response_templates['file_operation'].copy()
            
        elif any(keyword in user_input_lower for keyword in ['python', '代码', '编程', 'print', 'def']):
            template = self.response_templates['python_code'].copy()
            template['steps'][0]['args']['code'] = actual_request
            
        else:
            template = self.response_templates['default'].copy()
        
        # 返回JSON格式的计划
        return json.dumps(template, ensure_ascii=False, indent=2)
    
    def _extract_math_expression(self, text: str) -> Optional[str]:
        """
        从文本中提取数学表达式
        
        Args:
            text: 输入文本
            
        Returns:
            提取的数学表达式或None
        """
        import re
        
        # 匹配常见的数学表达式模式
        patterns = [
            r'[\d+\-*/().\s]+',  # 基本数学表达式
            r'\d+[\+\-\*/]\d+',  # 简单二元运算
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                # 返回最长的匹配
                return max(matches, key=len).strip()
        
        return None


class OpenAIClient(BaseLLMClient):
    """
    OpenAI API客户端
    
    真实的OpenAI API调用实现 (需要安装openai库)
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        try:
            import openai
            self.client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        except ImportError:
            raise ImportError("需要安装openai库: pip install openai")
    
    async def generate(self, system_prompt: str, user_prompt: str) -> str:
        """使用OpenAI API生成响应"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            return response.choices[0].message.content
        except Exception as e:
            raise RuntimeError(f"OpenAI API调用失败: {str(e)}")


class AnthropicClient(BaseLLMClient):
    """
    Anthropic Claude API客户端
    
    真实的Claude API调用实现 (需要安装anthropic库)
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        try:
            import anthropic
            self.client = anthropic.AsyncAnthropic(
                api_key=self.api_key,
                base_url=self.base_url
            )
        except ImportError:
            raise ImportError("需要安装anthropic库: pip install anthropic")
    
    async def generate(self, system_prompt: str, user_prompt: str) -> str:
        """使用Anthropic API生成响应"""
        try:
            response = await self.client.messages.create(
                model=self.model,
                system=system_prompt,
                messages=[{"role": "user", "content": user_prompt}],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            return response.content[0].text
        except Exception as e:
            raise RuntimeError(f"Anthropic API调用失败: {str(e)}")


class LLMClient:
    """
    LLM客户端工厂类
    
    根据配置自动选择合适的LLM客户端
    类比pytest的插件加载机制
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = self._create_client()
    
    def _create_client(self) -> BaseLLMClient:
        """
        根据配置创建LLM客户端

        Returns:
            LLM客户端实例
        """
        model = self.config.get('model', 'mock').lower()
        api_type = self.config.get('api_type', '').lower()
        base_url = self.config.get('base_url', '').lower()

        # 根据模型名称、API类型或base_url选择客户端
        if model == 'mock' or api_type == 'mock':
            return MockLLMClient(self.config)
        elif ('gpt' in model or 'openai' in api_type or
              'qwen' in model or 'glm' in model or 'baichuan' in model or
              'siliconflow' in base_url or 'deepseek' in base_url or
              'zhipu' in base_url or 'moonshot' in base_url):
            # 支持 OpenAI 兼容的 API (包括 Qwen, GLM, 硅基流动等)
            print(f"🔧 使用 OpenAI 兼容客户端，模型: {self.config.get('model')}")
            return OpenAIClient(self.config)
        elif 'claude' in model or 'anthropic' in api_type:
            return AnthropicClient(self.config)
        else:
            print(f"⚠️ 未知的模型类型: {model}，尝试使用 OpenAI 兼容客户端")
            return OpenAIClient(self.config)
    
    async def generate(self, system_prompt: str, user_prompt: str) -> str:
        """
        生成文本响应
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            生成的文本
        """
        return await self.client.generate(system_prompt, user_prompt)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'model': self.client.model,
            'type': self.client.__class__.__name__,
            'max_tokens': self.client.max_tokens,
            'temperature': self.client.temperature
        }


# 便捷函数
async def create_llm_client(config: Dict[str, Any]) -> LLMClient:
    """
    创建LLM客户端的便捷函数
    
    Args:
        config: 配置字典
        
    Returns:
        LLM客户端实例
    """
    return LLMClient(config)


# 测试函数
if __name__ == "__main__":
    async def test_llm_clients():
        """测试不同的LLM客户端"""
        
        # 测试Mock客户端
        mock_config = {
            'model': 'mock',
            'api_key': 'mock_key',
            'max_tokens': 1000,
            'temperature': 0.7
        }
        
        print("🧪 测试Mock LLM客户端:")
        print("-" * 30)
        
        mock_client = LLMClient(mock_config)
        
        test_prompts = [
            "计算 2 + 3 * 4",
            "读取文件 example.txt",
            "执行Python代码: print('Hello World')",
            "这是一个通用请求"
        ]
        
        for prompt in test_prompts:
            print(f"\n📝 用户输入: {prompt}")
            response = await mock_client.generate(
                system_prompt="你是一个AI助手",
                user_prompt=f"用户请求: {prompt}"
            )
            print(f"🤖 AI响应: {response[:100]}...")
        
        print(f"\n📊 模型信息: {mock_client.get_model_info()}")
        print("\n✅ 测试完成")
    
    asyncio.run(test_llm_clients())