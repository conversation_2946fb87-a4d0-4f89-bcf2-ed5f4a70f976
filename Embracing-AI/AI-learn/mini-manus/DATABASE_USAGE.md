# 数据库工具使用指南

## 🗄️ 概述

数据库工具是 mini-manus 项目的第四个核心工具，提供安全的数据库查询功能。

### ✨ 主要特性

- 🔒 **安全第一**: 仅支持 SELECT 查询，防止数据修改
- 🗃️ **多数据库支持**: SQLite、MySQL、PostgreSQL
- 🛡️ **SQL注入防护**: 自动检测和阻止危险SQL语句
- 📊 **结果格式化**: 表格形式显示查询结果
- ⏱️ **超时控制**: 防止长时间运行的查询

## 🚀 快速开始

### 1. 基本使用

```python
from tools.database import DatabaseTool
import asyncio

async def basic_usage():
    db_tool = DatabaseTool()
    
    # 连接SQLite数据库
    result = await db_tool.execute(
        action="connect",
        db_type="sqlite",
        connection_string="test.db"
    )
    print(result)
    
    # 执行查询
    result = await db_tool.execute(
        action="query",
        sql="SELECT * FROM users LIMIT 5"
    )
    print(result)
    
    # 断开连接
    await db_tool.execute(action="disconnect")

asyncio.run(basic_usage())
```

### 2. 支持的操作

| 操作 | 说明 | 示例 |
|------|------|------|
| `connect` | 连接数据库 | `{"action": "connect", "db_type": "sqlite", "connection_string": "test.db"}` |
| `query` | 执行SELECT查询 | `{"action": "query", "sql": "SELECT * FROM users"}` |
| `show_tables` | 显示所有表 | `{"action": "show_tables"}` |
| `describe` | 显示表结构 | `{"action": "describe", "table": "users"}` |
| `status` | 显示连接状态 | `{"action": "status"}` |
| `disconnect` | 断开连接 | `{"action": "disconnect"}` |

## 🔧 数据库连接

### SQLite (推荐用于学习)

```python
# 连接文件数据库
await db_tool.execute(
    action="connect",
    db_type="sqlite",
    connection_string="/path/to/database.db"
)

# 连接内存数据库
await db_tool.execute(
    action="connect",
    db_type="sqlite", 
    connection_string=":memory:"
)
```

### MySQL (需要安装 pymysql)

```bash
pip install pymysql
```

```python
# 方式1: 使用连接字符串
await db_tool.execute(
    action="connect",
    db_type="mysql",
    connection_string="mysql://user:password@localhost:3306/database"
)

# 方式2: 使用单独参数
await db_tool.execute(
    action="connect",
    db_type="mysql",
    host="localhost",
    port=3306,
    user="root",
    password="password",
    database="test"
)
```

### PostgreSQL (需要安装 psycopg2)

```bash
pip install psycopg2-binary
```

```python
# 使用连接字符串
await db_tool.execute(
    action="connect",
    db_type="postgresql",
    connection_string="postgresql://user:password@localhost:5432/database"
)
```

## 📊 查询示例

### 基本查询

```sql
-- 查询所有数据
SELECT * FROM employees

-- 条件查询
SELECT name, salary FROM employees WHERE department = '技术部'

-- 排序查询
SELECT * FROM employees ORDER BY salary DESC LIMIT 10
```

### 聚合查询

```sql
-- 统计查询
SELECT department, COUNT(*) as count, AVG(salary) as avg_salary 
FROM employees 
GROUP BY department

-- 复杂查询
SELECT name, salary 
FROM employees 
WHERE salary > (SELECT AVG(salary) FROM employees)
```

## 🔒 安全特性

### 允许的操作

✅ **SELECT** 查询  
✅ **JOIN** 连接  
✅ **WHERE** 条件  
✅ **GROUP BY** 分组  
✅ **ORDER BY** 排序  
✅ **LIMIT** 限制  

### 禁止的操作

❌ **INSERT** 插入  
❌ **UPDATE** 更新  
❌ **DELETE** 删除  
❌ **DROP** 删除表  
❌ **CREATE** 创建表  
❌ **ALTER** 修改表  

### SQL注入防护

```python
# 这些危险SQL会被自动阻止
dangerous_sql = [
    "DROP TABLE users",
    "INSERT INTO users VALUES (1, 'hacker')",
    "SELECT * FROM users; DROP TABLE users;",
    "SELECT * FROM users -- comment",
]
```

## 🧪 演示和测试

### 运行完整演示

```bash
python database_demo.py
```

### 运行交互式演示

```bash
python database_demo.py
# 选择 'y' 进入交互模式
```

### 测试退出处理

```bash
python test_database_exit.py
```

## ⚠️ 注意事项

1. **数据库驱动**: MySQL和PostgreSQL需要额外安装驱动
2. **权限控制**: 确保数据库用户只有SELECT权限
3. **连接管理**: 使用完毕后记得断开连接
4. **结果限制**: 默认最多返回100行结果
5. **超时设置**: 查询超时时间默认30秒

## 🐛 常见问题

### Q: 连接MySQL时报错 "No module named 'pymysql'"
A: 需要安装MySQL驱动：`pip install pymysql`

### Q: 查询结果被截断
A: 可以在配置中调整 `max_rows` 参数

### Q: 退出时出现异常
A: 已修复，确保使用最新版本的代码

### Q: 无法执行INSERT语句
A: 这是安全特性，工具仅支持SELECT查询

## 📚 更多示例

查看以下文件获取更多使用示例：

- `database_demo.py` - 完整功能演示
- `test_database_exit.py` - 退出处理测试
- `tools/database.py` - 源代码和内置测试

## 🤝 贡献

如果发现问题或有改进建议，欢迎提交Issue或PR！
