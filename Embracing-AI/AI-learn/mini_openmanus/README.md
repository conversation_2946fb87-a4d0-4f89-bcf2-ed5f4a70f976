<p align="center">
  <img src="assets/logo.jpg" width="200"/>
</p>

English | [中文](README_zh.md) | [한국어](README_ko.md) | [日本語](README_ja.md)

[![GitHub stars](https://img.shields.io/github/stars/FoundationAgents/OpenManus?style=social)](https://github.com/FoundationAgents/OpenManus/stargazers)
&ensp;
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT) &ensp;
[![Discord Follow](https://dcbadge.vercel.app/api/server/DYn29wFk9z?style=flat)](https://discord.gg/DYn29wFk9z)
[![Demo](https://img.shields.io/badge/Demo-Hugging%20Face-yellow)](https://huggingface.co/spaces/lyh-917/OpenManusDemo)
[![DOI](https://zenodo.org/badge/DOI/10.5281/zenodo.15186407.svg)](https://doi.org/10.5281/zenodo.15186407)

# 👋 OpenManus

Manus is incredible, but OpenManus can achieve any idea without an *Invite Code* 🛫!

Our team members [@Xinbin Liang](https://github.com/mannaandpoem) and [@Jinyu Xiang](https://github.com/XiangJinyu) (core authors), along with [@Zhaoyang Yu](https://github.com/MoshiQAQ), [@Jiayi Zhang](https://github.com/didiforgithub), and [@Sirui Hong](https://github.com/stellaHSR), we are from [@MetaGPT](https://github.com/geekan/MetaGPT). The prototype is launched within 3 hours and we are keeping building!

It's a simple implementation, so we welcome any suggestions, contributions, and feedback!

Enjoy your own agent with OpenManus!

We're also excited to introduce [OpenManus-RL](https://github.com/OpenManus/OpenManus-RL), an open-source project dedicated to reinforcement learning (RL)- based (such as GRPO) tuning methods for LLM agents, developed collaboratively by researchers from UIUC and OpenManus.

## Project Demo

<video src="https://private-user-images.githubusercontent.com/61239030/420168772-6dcfd0d2-9142-45d9-b74e-d10aa75073c6.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDEzMTgwNTksIm5iZiI6MTc0MTMxNzc1OSwicGF0aCI6Ii82MTIzOTAzMC80MjAxNjg3NzItNmRjZmQwZDItOTE0Mi00NWQ5LWI3NGUtZDEwYWE3NTA3M2M2Lm1wND9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTAzMDclMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwMzA3VDAzMjIzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTdiZjFkNjlmYWNjMmEzOTliM2Y3M2VlYjgyNDRlZDJmOWE3NWZhZjE1MzhiZWY4YmQ3NjdkNTYwYTU5ZDA2MzYmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.UuHQCgWYkh0OQq9qsUWqGsUbhG3i9jcZDAMeHjLt5T4" data-canonical-src="https://private-user-images.githubusercontent.com/61239030/420168772-6dcfd0d2-9142-45d9-b74e-d10aa75073c6.mp4?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDEzMTgwNTksIm5iZiI6MTc0MTMxNzc1OSwicGF0aCI6Ii82MTIzOTAzMC80MjAxNjg3NzItNmRjZmQwZDItOTE0Mi00NWQ5LWI3NGUtZDEwYWE3NTA3M2M2Lm1wND9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTAzMDclMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwMzA3VDAzMjIzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTdiZjFkNjlmYWNjMmEzOTliM2Y3M2VlYjgyNDRlZDJmOWE3NWZhZjE1MzhiZWY4YmQ3NjdkNTYwYTU5ZDA2MzYmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.UuHQCgWYkh0OQq9qsUWqGsUbhG3i9jcZDAMeHjLt5T4" controls="controls" muted="muted" class="d-block rounded-bottom-2 border-top width-fit" style="max-height:640px; min-height: 200px"></video>

## Installation

We provide two installation methods. Method 2 (using uv) is recommended for faster installation and better dependency management.

### Method 1: Using conda

1. Create a new conda environment:

```bash
conda create -n open_manus python=3.12
conda activate open_manus
```

2. Clone the repository:

```bash
git clone https://github.com/FoundationAgents/OpenManus.git
cd OpenManus
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

### Method 2: Using uv (Recommended)

1. Install uv (A fast Python package installer and resolver):

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Clone the repository:

```bash
git clone https://github.com/FoundationAgents/OpenManus.git
cd OpenManus
```

3. Create a new virtual environment and activate it:

```bash
uv venv --python 3.12
source .venv/bin/activate  # On Unix/macOS
# Or on Windows:
# .venv\Scripts\activate
```

4. Install dependencies:

```bash
uv pip install -r requirements.txt
```

### Browser Automation Tool (Optional)
```bash
playwright install
```

## Configuration

OpenManus requires configuration for the LLM APIs it uses. Follow these steps to set up your configuration:

1. Create a `config.toml` file in the `config` directory (you can copy from the example):

```bash
cp config/config.example.toml config/config.toml
```

2. Edit `config/config.toml` to add your API keys and customize settings:

```toml
# Global LLM configuration
[llm]
model = "gpt-4o"
base_url = "https://api.openai.com/v1"
api_key = "sk-..."  # Replace with your actual API key
max_tokens = 4096
temperature = 0.0

# Optional configuration for specific LLM models
[llm.vision]
model = "gpt-4o"
base_url = "https://api.openai.com/v1"
api_key = "sk-..."  # Replace with your actual API key
```

## Quick Start

One line for run OpenManus:

```bash
python main.py
```

Then input your idea via terminal!

For MCP tool version, you can run:
```bash
python run_mcp.py
```

For unstable multi-agent version, you also can run:

```bash
python run_flow.py
```

### Custom Adding Multiple Agents

Currently, besides the general OpenManus Agent, we have also integrated the DataAnalysis Agent, which is suitable for data analysis and data visualization tasks. You can add this agent to `run_flow` in `config.toml`.

```toml
# Optional configuration for run-flow
[runflow]
use_data_analysis_agent = true     # Disabled by default, change to true to activate
```
In addition, you need to install the relevant dependencies to ensure the agent runs properly: [Detailed Installation Guide](app/tool/chart_visualization/README.md##Installation)

## How to contribute

We welcome any friendly suggestions and helpful contributions! Just create issues or submit pull requests.

Or contact @mannaandpoem via 📧email: <EMAIL>

**Note**: Before submitting a pull request, please use the pre-commit tool to check your changes. Run `pre-commit run --all-files` to execute the checks.

## Community Group
Join our networking group on Feishu and share your experience with other developers!

<div align="center" style="display: flex; gap: 20px;">
    <img src="assets/community_group.jpg" alt="OpenManus 交流群" width="300" />
</div>

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=FoundationAgents/OpenManus&type=Date)](https://star-history.com/#FoundationAgents/OpenManus&Date)

## Sponsors
Thanks to [PPIO](https://ppinfra.com/user/register?invited_by=OCPKCN&utm_source=github_openmanus&utm_medium=github_readme&utm_campaign=link) for computing source support.
> PPIO: The most affordable and easily-integrated MaaS and GPU cloud solution.


## Acknowledgement

Thanks to [anthropic-computer-use](https://github.com/anthropics/anthropic-quickstarts/tree/main/computer-use-demo)
and [browser-use](https://github.com/browser-use/browser-use) for providing basic support for this project!

Additionally, we are grateful to [AAAJ](https://github.com/metauto-ai/agent-as-a-judge), [MetaGPT](https://github.com/geekan/MetaGPT), [OpenHands](https://github.com/All-Hands-AI/OpenHands) and [SWE-agent](https://github.com/SWE-agent/SWE-agent).

We also thank stepfun(阶跃星辰) for supporting our Hugging Face demo space.

OpenManus is built by contributors from MetaGPT. Huge thanks to this agent community!

## Cite
```bibtex
@misc{openmanus2025,
  author = {Xinbin Liang and Jinyu Xiang and Zhaoyang Yu and Jiayi Zhang and Sirui Hong and Sheng Fan and Xiao Tang},
  title = {OpenManus: An open-source framework for building general AI agents},
  year = {2025},
  publisher = {Zenodo},
  doi = {10.5281/zenodo.15186407},
  url = {https://doi.org/10.5281/zenodo.15186407},
}
```

# OpenManus 迷你版

一个简化版的AI智能体框架，用于学习和理解OpenManus的核心架构设计。

## 🎯 项目目标

通过构建一个迷你版本，帮助开发者快速理解：
- AI智能体的基本架构
- 工具系统的设计模式
- 配置管理的最佳实践
- 异步编程在AI应用中的应用

## 🏗️ 架构设计

### 核心组件

```
用户输入 → Agent选择 → Tool执行 → 结果反馈
   ↓         ↓         ↓         ↓
"计算2+3" → MiniManus → Calculator → "结果: 5"
```

### 类比pytest框架

| OpenManus组件 | pytest对应组件 | 作用 |
|---------------|----------------|------|
| Agent | TestCase | 组织和执行逻辑 |
| Tool | Fixture | 提供具体功能 |
| ToolCollection | 插件系统 | 管理工具集合 |
| Config | pytest.ini | 配置管理 |

## 🚀 快速开始

### 1. 安装依赖

```bash
# 基础运行（使用Mock模式）
pip install asyncio

# 如果要使用真实的LLM API
pip install openai anthropic
```

### 2. 运行演示

```bash
# 直接运行（使用Mock LLM）
python mini_openmanus/main.py

# 或者使用模块方式
python -m mini_openmanus.main
```

### 3. 交互示例

```
🤖 欢迎使用 OpenManus 迷你版!
==================================================
✅ 配置加载成功，使用模型: mock
🚀 智能体 'MiniManus' 初始化完成
📦 可用工具: calculator, file_editor, python_executor

💡 输入您的指令，输入 'quit' 退出
------------------------------

👤 您: 计算 2 + 3 * 4

🤔 智能体正在思考...
📋 执行计划: 数学计算任务
📍 执行步骤 1/1: 执行数学计算
✅ calculator: 计算结果: 2 + 3 * 4 = 14

🤖 智能体: 步骤1: ✅ calculator: 计算结果: 2 + 3 * 4 = 14
```

## 📁 项目结构

```
mini_openmanus/
├── __init__.py          # 包初始化
├── main.py             # 主入口文件
├── config.py           # 配置管理
├── agent.py            # 智能体核心
├── llm.py              # LLM客户端
├── tools/              # 工具系统
│   ├── __init__.py     # 工具基类
│   ├── calculator.py   # 计算器工具
│   ├── file_editor.py  # 文件编辑工具
│   └── python_executor.py # Python执行工具
├── config.example.json # 配置示例
└── README.md          # 项目说明
```

## 🔧 配置说明

### 基础配置 (config.json)

```json
{
  "model": "mock",
  "api_key": "mock_key",
  "max_tokens": 1000,
  "temperature": 0.7,
  "tools": {
    "calculator": {"enabled": true},
    "file_editor": {"enabled": true},
    "python_executor": {"enabled": true}
  }
}
```

### 使用真实LLM

```json
{
  "model": "gpt-3.5-turbo",
  "api_key": "your-openai-api-key",
  "base_url": "https://api.openai.com/v1",
  "max_tokens": 2000,
  "temperature": 0.7
}
```

## 🛠️ 扩展开发

### 1. 创建自定义工具

```python
from mini_openmanus.tools import BaseTool

class MyCustomTool(BaseTool):
    def __init__(self):
        super().__init__()
        self.name = "my_tool"
        self.description = "我的自定义工具"
    
    async def execute(self, **kwargs) -> str:
        # 实现你的工具逻辑
        return "工具执行结果"
```

### 2. 创建专业智能体

```python
from mini_openmanus.agent import BaseAgent

class MySpecialAgent(BaseAgent):
    def _setup_tools(self):
        # 添加专门的工具
        self.tools.add_tool(MyCustomTool())
    
    async def _generate_plan(self, user_input: str):
        # 实现专门的计划生成逻辑
        return {"summary": "专业任务", "steps": [...]}
```

## 🧪 测试和调试

### 运行工具测试

```bash
# 测试计算器
python mini_openmanus/tools/calculator.py

# 测试LLM客户端
python mini_openmanus/llm.py

# 生成配置文件
python mini_openmanus/config.py
```

### Mock模式优势

- ✅ 无需API密钥即可运行
- ✅ 响应速度快，便于调试
- ✅ 可预测的行为，便于测试
- ✅ 支持离线开发

## 📚 学习要点

### 1. 异步编程模式

```python
# 所有工具都是异步的
async def execute(self, **kwargs) -> str:
    # 异步操作
    await asyncio.sleep(0.1)
    return result

# 智能体执行也是异步的
result = await agent.run(user_input)
```

### 2. 插件化架构

```python
# 工具注册机制
tools = ToolCollection()
tools.add_tool(Calculator())
tools.add_tool(FileEditor())

# 动态工具调用
tool = tools.get_tool("calculator")
result = await tool.execute(expression="2+3")
```

### 3. 配置驱动设计

```python
# 配置决定行为
if config["tools"]["calculator"]["enabled"]:
    self.tools.add_tool(Calculator())
```

## 🔍 与原版OpenManus的对比

| 特性 | 原版OpenManus | 迷你版 |
|------|---------------|--------|
| 复杂度 | 生产级，功能完整 | 简化版，易于理解 |
| 依赖 | 多个外部库 | 最小依赖 |
| 工具数量 | 20+ | 3个核心工具 |
| LLM支持 | 多种API | Mock + 基础API |
| 配置格式 | TOML | JSON |
| 部署方式 | 多种模式 | 单一模式 |

## 🎓 学习路径

1. **理解架构** - 阅读 `agent.py` 了解智能体设计
2. **学习工具系统** - 查看 `tools/` 目录的实现
3. **掌握配置管理** - 研究 `config.py` 的设计模式
4. **实践扩展** - 尝试添加自定义工具和智能体
5. **对比学习** - 与原版OpenManus进行对比

## 🤝 贡献指南

欢迎提交Issue和PR来改进这个学习项目！

## 📄 许可证

MIT License - 仅用于学习和教育目的
